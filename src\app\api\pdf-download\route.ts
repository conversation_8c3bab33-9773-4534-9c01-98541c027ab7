import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const downloadUrl = searchParams.get('url');

    if (!downloadUrl) {
      return NextResponse.json({ error: 'Download URL is required' }, { status: 400 });
    }

    const API_KEY = process.env.NEXT_PUBLIC_PDF_API_KEY;
    const API_BASE_URL = process.env.NEXT_PUBLIC_PDF_API_URL;

    if (!API_KEY) {
      return NextResponse.json({ error: 'API key not configured' }, { status: 500 });
    }

    if (!API_BASE_URL) {
      return NextResponse.json({ error: 'API base URL not configured' }, { status: 500 });
    }

    console.log('API Base URL:', API_BASE_URL);
    console.log('Original download URL:', downloadUrl);

    // Fix the download URL to use the correct API base URL
    // The API might return URLs with localhost:8000, but we need to use the configured URL
    let correctedDownloadUrl = downloadUrl;

    // If the download URL starts with localhost:8000, replace it with the configured API URL
    if (downloadUrl.includes('localhost:8000')) {
      correctedDownloadUrl = downloadUrl.replace('http://localhost:8000', API_BASE_URL);
      console.log('Corrected download URL:', correctedDownloadUrl);
    }

    // Validate that the corrected download URL is from the expected API
    if (!correctedDownloadUrl.startsWith(API_BASE_URL)) {
      return NextResponse.json({ error: 'Invalid download URL' }, { status: 400 });
    }

    // Fetch the file from the PDF compression service
    const response = await fetch(correctedDownloadUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Accept': 'application/pdf'
      }
    });

    console.log('Download response status:', response.status);
    console.log('Download response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Download failed:', response.status, response.statusText, errorText);
      return NextResponse.json(
        { error: `Download failed: ${response.status} ${response.statusText}`, details: errorText },
        { status: response.status }
      );
    }

    // Get the file data
    const fileBuffer = await response.arrayBuffer();
    
    // Extract filename from the original response headers or use default
    const contentDisposition = response.headers.get('content-disposition');
    let filename = 'compressed_document.pdf';
    
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
      if (filenameMatch) {
        filename = filenameMatch[1];
      }
    }

    // Return the file with appropriate headers
    return new NextResponse(fileBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': fileBuffer.byteLength.toString(),
      },
    });

  } catch (error) {
    console.error('Proxy download error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json(
      { error: 'Internal server error during download', details: errorMessage },
      { status: 500 }
    );
  }
}
